// scripts/manage-users.js
const { Pool } = require('pg');
const bcrypt = require('bcryptjs');
const readline = require('readline');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const pool = new Pool({
  connectionString: process.env.DATABASE_URL,
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
});

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

function question(prompt) {
  return new Promise((resolve) => {
    rl.question(prompt, resolve);
  });
}

async function listUsers() {
  try {
    const result = await pool.query(`
      SELECT id, email, is_verified, created_at 
      FROM users 
      ORDER BY created_at DESC
    `);
    
    console.log('\n📋 All Users:');
    console.log('ID | Email | Verified | Created');
    console.log('---|-------|----------|--------');
    
    result.rows.forEach(user => {
      const verified = user.is_verified ? '✅' : '❌';
      const date = user.created_at.toLocaleDateString();
      console.log(`${user.id} | ${user.email} | ${verified} | ${date}`);
    });
    
    console.log(`\nTotal users: ${result.rows.length}`);
  } catch (error) {
    console.error('Error listing users:', error.message);
  }
}

async function addUser() {
  try {
    const email = await question('Enter email (@veltech.edu.in): ');
    
    if (!email.endsWith('@veltech.edu.in')) {
      console.log('❌ Email must end with @veltech.edu.in');
      return;
    }
    
    const password = await question('Enter password: ');
    const verified = await question('Mark as verified? (y/n): ');
    
    const hashedPassword = await bcrypt.hash(password, 12);
    const isVerified = verified.toLowerCase() === 'y';
    
    const result = await pool.query(`
      INSERT INTO users (email, password_hash, is_verified) 
      VALUES ($1, $2, $3) 
      RETURNING id, email
    `, [email, hashedPassword, isVerified]);
    
    console.log(`✅ User created: ${result.rows[0].email} (ID: ${result.rows[0].id})`);
  } catch (error) {
    if (error.code === '23505') {
      console.log('❌ User with this email already exists');
    } else {
      console.error('Error adding user:', error.message);
    }
  }
}

async function deleteUser() {
  try {
    const email = await question('Enter email to delete: ');
    
    const result = await pool.query('DELETE FROM users WHERE email = $1 RETURNING email', [email]);
    
    if (result.rows.length > 0) {
      console.log(`✅ User deleted: ${result.rows[0].email}`);
    } else {
      console.log('❌ User not found');
    }
  } catch (error) {
    console.error('Error deleting user:', error.message);
  }
}

async function verifyUser() {
  try {
    const email = await question('Enter email to verify: ');
    
    const result = await pool.query(`
      UPDATE users 
      SET is_verified = true, verification_token = null, verification_token_expires = null 
      WHERE email = $1 
      RETURNING email
    `, [email]);
    
    if (result.rows.length > 0) {
      console.log(`✅ User verified: ${result.rows[0].email}`);
    } else {
      console.log('❌ User not found');
    }
  } catch (error) {
    console.error('Error verifying user:', error.message);
  }
}

async function showUnverified() {
  try {
    const result = await pool.query(`
      SELECT email, created_at 
      FROM users 
      WHERE is_verified = false 
      ORDER BY created_at DESC
    `);
    
    console.log('\n📋 Unverified Users:');
    if (result.rows.length === 0) {
      console.log('No unverified users found.');
      return;
    }
    
    result.rows.forEach(user => {
      const date = user.created_at.toLocaleDateString();
      console.log(`- ${user.email} (${date})`);
    });
  } catch (error) {
    console.error('Error listing unverified users:', error.message);
  }
}

async function main() {
  console.log('🔧 VTU-GPT User Management Tool\n');
  
  while (true) {
    console.log('\nChoose an option:');
    console.log('1. List all users');
    console.log('2. Add new user');
    console.log('3. Delete user');
    console.log('4. Verify user email');
    console.log('5. Show unverified users');
    console.log('6. Exit');
    
    const choice = await question('\nEnter your choice (1-6): ');
    
    switch (choice) {
      case '1':
        await listUsers();
        break;
      case '2':
        await addUser();
        break;
      case '3':
        await deleteUser();
        break;
      case '4':
        await verifyUser();
        break;
      case '5':
        await showUnverified();
        break;
      case '6':
        console.log('👋 Goodbye!');
        rl.close();
        await pool.end();
        process.exit(0);
      default:
        console.log('❌ Invalid choice. Please enter 1-6.');
    }
  }
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = { listUsers, addUser, deleteUser, verifyUser };
