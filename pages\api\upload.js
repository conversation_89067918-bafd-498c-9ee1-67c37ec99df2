import formidable from 'formidable';

export const config = {
  api: {
    bodyParser: false, // Disable Next.js body parser to use formidable
  },
};

const handler = async (req, res) => {
  if (req.method === 'POST') {
    try {
      const form = formidable({
        uploadDir: './uploads', // Directory where files will be uploaded
        keepExtensions: true, // Keep original file extensions
        multiples: true, // Allow multiple file uploads
        filename: (name, ext, part, form) => {
          // Custom filename generation if needed
          return `${Date.now()}-${part.originalFilename}`;
        },
      });

      // Parse the form data
      form.parse(req, (err, fields, files) => {
        if (err) {
          return res.status(500).json({ message: 'Error during file parsing', error: err });
        }

        // Check if files exist and return file data
        if (files.files) {
          const uploadedFiles = Array.isArray(files.files) ? files.files : [files.files]; // Handle single or multiple files
          const filePaths = uploadedFiles.map(file => file.filepath);

          return res.status(200).json({
            message: 'Files uploaded successfully',
            filePaths: filePaths, // List of file paths
            fileNames: uploadedFiles.map(file => file.originalFilename), // Return original filenames
          });
        } else {
          return res.status(400).json({ message: 'No files were uploaded' });
        }
      });
    } catch (error) {
      res.status(500).json({ message: 'Error during file upload', error: error.message });
    }
  } else {
    res.status(405).json({ message: 'Method Not Allowed' });
  }
};

export default handler;
