import axios from 'axios';

export default async function handler(req, res) {
  if (req.method === 'POST') {
    const { query } = req.body;

    console.log('Chat API called with query:', query);

    // Check if OpenAI API key is configured
    const apiKey = process.env.OPENAI_API_KEY;
    if (!apiKey || apiKey === 'YOUR_OPENAI_API_KEY') {
      console.log('OpenAI API key not configured, returning fallback response');
      return res.status(200).json({
        reply: 'Hello! I\'m VTU GPT. I\'m currently in demo mode as the OpenAI API key is not configured. Please contact the administrator to set up the API key for full functionality.'
      });
    }

    try {
      const response = await axios.post(
        'https://api.openai.com/v1/chat/completions',
        {
          model: 'gpt-3.5-turbo',
          messages: [
            {
              role: 'system',
              content: 'You are VTU GPT, a helpful AI assistant for Veltech University students and faculty. Provide helpful, accurate, and friendly responses about university-related topics, academics, and general questions.'
            },
            {
              role: 'user',
              content: query
            }
          ],
          max_tokens: 150,
          temperature: 0.7,
        },
        {
          headers: {
            'Authorization': `Bearer ${apiKey}`,
            'Content-Type': 'application/json',
          },
        }
      );

      const reply = response.data.choices[0].message.content;
      return res.status(200).json({ reply });
    } catch (error) {
      console.error('OpenAI API error:', {
        message: error.message,
        status: error.response?.status,
        statusText: error.response?.statusText,
        data: error.response?.data
      });
      return res.status(500).json({ reply: 'Sorry, I\'m experiencing technical difficulties. Please try again later.' });
    }
  }

  return res.status(405).json({ message: 'Method Not Allowed' });
}
