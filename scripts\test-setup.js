// scripts/test-setup.js
const { Pool } = require('pg');
const nodemailer = require('nodemailer');

async function testSetup() {
  console.log('🔍 Testing VTU-GPT Authentication Setup...\n');

  // Load environment variables
  require('dotenv').config({ path: '.env.local' });

  let allTestsPassed = true;

  // Test 1: Environment Variables
  console.log('1️⃣ Testing Environment Variables...');
  const requiredEnvVars = [
    'DATABASE_URL',
    'JWT_SECRET',
    'EMAIL_HOST',
    'EMAIL_PORT',
    'EMAIL_USER',
    'EMAIL_PASS',
    'EMAIL_FROM',
    'APP_URL'
  ];

  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.log(`   ❌ Missing: ${envVar}`);
      allTestsPassed = false;
    } else {
      console.log(`   ✅ Found: ${envVar}`);
    }
  }

  // Test 2: Database Connection
  console.log('\n2️⃣ Testing Database Connection...');
  try {
    const pool = new Pool({
      connectionString: process.env.DATABASE_URL,
      ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    });

    const result = await pool.query('SELECT NOW()');
    console.log('   ✅ Database connection successful');

    // Check if users table exists
    const tableCheck = await pool.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables
        WHERE table_schema = 'public'
        AND table_name = 'users'
      );
    `);

    if (tableCheck.rows[0].exists) {
      console.log('   ✅ Users table exists');
    } else {
      console.log('   ❌ Users table not found. Run: npm run setup-db');
      allTestsPassed = false;
    }

    await pool.end();
  } catch (error) {
    console.log(`   ❌ Database connection failed: ${error.message}`);
    allTestsPassed = false;
  }

  // Test 3: Email Configuration
  console.log('\n3️⃣ Testing Email Configuration...');
  try {
    const transporter = nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false,
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS,
      },
    });

    await transporter.verify();
    console.log('   ✅ Email configuration valid');
  } catch (error) {
    console.log(`   ❌ Email configuration failed: ${error.message}`);
    console.log('   💡 Make sure you\'re using an App Password for Gmail');
    allTestsPassed = false;
  }

  // Test 4: JWT Secret
  console.log('\n4️⃣ Testing JWT Secret...');
  if (process.env.JWT_SECRET && process.env.JWT_SECRET.length >= 32) {
    console.log('   ✅ JWT secret is sufficiently long');
  } else {
    console.log('   ⚠️  JWT secret should be at least 32 characters long');
    console.log('   💡 Generate a secure random string for production');
  }

  // Summary
  console.log('\n📋 Setup Test Summary:');
  if (allTestsPassed) {
    console.log('🎉 All tests passed! Your VTU-GPT authentication system is ready.');
    console.log('\n🚀 Next steps:');
    console.log('   1. Start the development server: npm run dev');
    console.log('   2. Visit http://localhost:3000');
    console.log('   3. Try registering with a @veltech.edu.in email');
  } else {
    console.log('❌ Some tests failed. Please fix the issues above before proceeding.');
    console.log('\n📖 Check the README.md for detailed setup instructions.');
  }
}

if (require.main === module) {
  testSetup().catch(console.error);
}

module.exports = testSetup;
