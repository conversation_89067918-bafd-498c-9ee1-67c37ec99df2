/* Global Styles */
@import '@fortawesome/fontawesome-free/css/all.css';

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  height: 100%;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

/* Default body for non-chatbot pages */
body {
  display: flex;
  flex-direction: column;
}

/* Chatbot specific body styling */
body.chatbot-page {
  overflow: hidden; /* Prevent scrolling only for chatbot */
}

/* Base responsive font sizes */
html {
  font-size: 16px;
}

@media (max-width: 480px) {
  html {
    font-size: 14px;
  }
}

@media (min-width: 1200px) {
  html {
    font-size: 18px;
  }
}

/* Splash Screen */
.splash-screen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ffffff;
  z-index: 9999;
}

.splash-logo {
  width: 250px;
  height: auto;
  animation: fadeIn 1.5s ease-in-out;
  max-width: 90vw; /* Ensure it fits on small screens */
  margin-bottom: 2rem;
}

.loading-logo {
  margin-bottom: 1rem;
}

.splash-content {
  text-align: center;
  color: #333;
}

.splash-content .spinner {
  font-size: 2rem;
  color: #007bff;
  animation: spin 2s linear infinite;
  margin-bottom: 1rem;
}

.splash-content .loading-text {
  font-size: 1.1rem;
  color: #666;
  font-weight: 500;
}

.splash-content .welcome-text {
  font-size: 1.5rem;
  color: #333;
  font-weight: bold;
  background: linear-gradient(to right, #f10606, #66667a);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

@media (max-width: 480px) {
  .splash-logo {
    width: 160px; /* Further reduced for mobile */
    margin-bottom: 1.5rem;
  }

  .splash-content .welcome-text {
    font-size: 1.25rem;
  }

  .splash-content .loading-text {
    font-size: 1rem;
  }
}

@media (max-width: 320px) {
  .splash-logo {
    width: 120px; /* Much smaller for very small screens */
    margin-bottom: 1rem;
  }

  .splash-content .welcome-text {
    font-size: 1.1rem;
  }
}

@keyframes fadeIn {
  0% { opacity: 0; transform: scale(0.8); }
  100% { opacity: 1; transform: scale(1); }
}

/* Left Panel */
.left-panel {
  top: -60px;
  background: white;
  padding: 20px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 100vh; /* Make sure it spans the full height */
  overflow-y: auto;
  position: relative;
  transition: left 0.3s ease;
  font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between; /* Title and icon are spaced out */
  align-items: center;
  padding: 10px 20px;
  position: fixed; /* Keeps the header fixed at the top */
  top: 0;
  left: calc(100%-20%);
  width: 100%; /* Stretches header across the page */
  z-index: 100; /* Ensures it stays above other elements */
  box-shadow: 0 2px 10px rgba(74, 71, 71, 0.3);
  background: linear-gradient(rgba(255, 255, 255, 0.98), rgba(250, 250, 250, 0.98));
}

.header-title {
  font-size: 30px;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
  font-weight: bold;
  text-align: center;
  flex: auto;
  /* Gradient Effect */
  background: linear-gradient(to right, #f10606, #66667a);
  -webkit-background-clip: text; /* For Safari & Chrome */
  background-clip: text; /* Standard property */
  -webkit-text-fill-color: transparent; /* For proper visibility */
}



.user-icon {
  margin-left: auto; /* Ensures the icon stays at the far-right end */
}

.user-icon a {
  color: black;
  font-size: 30px;
  text-decoration: none;
  transition: color 0.3s ease;
}

.user-icon a:hover {
  color: grey; /* Changes color on hover */
}

/* User Section Styles */
.user-section {
  position: relative;
  margin-left: auto;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  padding: 8px 12px;
  border-radius: 20px;
  transition: background-color 0.3s ease;
}

.user-info:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.user-email {
  font-size: 14px;
  color: #333;
  max-width: 150px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-icon {
  font-size: 24px;
  color: #333;
}

.dropdown-icon {
  font-size: 12px;
  color: #666;
  transition: transform 0.3s ease;
}

.user-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #ddd;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
}

.user-menu-item {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  font-size: 14px;
}

.user-menu-item:hover {
  background-color: #f8f9fa;
}

.user-menu-item.user-email-display {
  cursor: default;
  background-color: #f8f9fa;
  color: #666;
}

.user-menu-item.user-email-display:hover {
  background-color: #f8f9fa;
}



.user-menu-divider {
  height: 1px;
  background-color: #eee;
  margin: 0;
}

.user-menu-item i {
  width: 16px;
  text-align: center;
}

/* Responsive adjustments for user section */
@media (max-width: 768px) {
  .user-email {
    display: none;
  }

  .user-info {
    padding: 8px;
  }

  .user-menu {
    min-width: 180px;
    right: -10px; /* Adjust position for mobile */
  }
}

@media (max-width: 480px) {
  .header-title {
    font-size: 1.5rem;
  }

  .user-menu {
    min-width: 160px;
    right: -5px;
  }

  .user-menu-item {
    padding: 10px 12px;
    font-size: 0.85rem;
  }
}

@media (max-width: 320px) {
  .header-title {
    font-size: 1.25rem;
  }

  .user-menu {
    min-width: 140px;
    right: 0;
  }

  .user-menu-item {
    padding: 8px 10px;
    font-size: 0.8rem;
  }
}

.chatbot-container {
  margin-top: 60px; /* Adjusts margin to account for fixed header */
  display: flex;
  flex-direction: column;
  height: 93vh;
  font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

.logo-img {
  width: 275px;
}

.new-chat-btn {
  background-color: #007bff;
  color: white;
  padding: 13px 15px;
  border: none;
  font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
  font-size:large;
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 20px;
  transition: background-color 0.3s ease;
  margin-top: -110px; /* Adjust margin to maintain spacing with much higher logo */
}

.new-chat-btn:hover {
  background-color: #0056b3;
}

.suggested-questions h3{
  color: #007bff;
  margin-bottom: 10px;
  margin-top: -50px;
  padding: 5px 10px;
  font-size: larger;
}

.suggested-questions ul {
  margin-top: 10px;
  list-style: none;
  font-size: large;
}

.suggested-questions li {
  padding: 10px 10px;
  color: black;
  cursor: pointer;
  transition: transform 0.3s ease-in-out;
  transform-origin: left; /* Set origin to the left */

}

.suggested-questions li:hover {
  transform: scale(1.07);
  color: grey;
}

.contact-details h3 {
  color: #007bff;
  margin-bottom: 10px;
  margin-top: -50px;
  padding: 5px 10px;
  font-size: larger;
}

.contact-details a{
  display: flex;
  align-items: center;
  gap: 10px;
  font-size:large;
  padding: 10px 10px;
  color: black;
  text-decoration: none;
  transition: transform 0.3s ease-in-out;
  transform-origin: left; /* Set origin to the left */
}

.contact-details a:hover{
  color: grey;
  transform: scale(1.07);

}

/* Social Media Links */
.social-media-links {
  margin-top: -50px;
  margin-bottom: 50px;

}

.social-media-links h3 {
  color: #007bff;
  margin-top: 10px;
  margin-bottom: 10px;
  padding: 5px 10px;

}

.social-media-links a {
  margin-right: 10px;
  font-size: large;
  color: black;
  text-decoration: none;
  padding: 5px ;
}

.social-media-links a:hover{
  color: grey;
}

/* Chatbot Panel */
.chatbot-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(200, 196, 196, 0.8)),
    url('/images/chatbot-background.jpg') no-repeat center center fixed;
  background-size: cover;
  background-position: center;
  border-radius: 0;
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  height: 100%;
}

.chat-history {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  padding: 10px;
  display: flex;
  flex-direction: column;
  gap: 10px;
}

/* Messages */
.message {
  padding: 10px;
  margin: 5px 0;
  border-radius: 8px;
  max-width: 70%;
  word-wrap: break-word;
}

.message.user {
  background-color: white;
  align-self: flex-end;
  text-align: right;
  font-style: italic;
  border-radius: 20px 20px 0 20px ;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
}

.message.chatbot {
  background-color: white;
  align-self: flex-start;
  text-align: left;
  font-style: italic;
  border-radius: 20px 20px 20px 0;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
}

/* Input Area */
.input-area {
  display: flex;
  gap: 10px;
  align-items: center;
  padding: 10px;
}

input {
  flex: 1;
  font-size:medium;
  padding: 15px;
  border-radius: 50px;
  border: 2px solid grey;
  font-family:'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;

}

.input-area-button {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 15px 20px;
  border-radius: 10px;
  cursor: pointer;
}

.input-area-button:hover {
  background-color: #0056b3;
}

/* Error Messages */
.error {
  color: red;
  font-size: 14px;
}



/* For larger screens, left panel and chatbot-panel align horizontally */
@media (min-width: 768px) {
  .chatbot-container {
    flex-direction: row;
  }

  .header {
    display: flex;
    justify-content: space-between; /* Title and icon are spaced out */
    align-items: center;
    padding: 10px 20px;
    position: fixed; /* Keeps the header fixed at the top */
    top: 0;
    left: calc(100%-20%);
    width: 100%; /* Stretches header across the page */
    z-index: 100; /* Ensures it stays above other elements */
    box-shadow: 320px 4px 5px rgba(74, 71, 71, 0.92);

  }


  .logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    margin-top: -80px; /* Move logo much higher for larger screens */
  }

  .toggle-left-panel {
    display: none; /* Hide the toggle button on larger screens */
  }

  .left-panel {
    top: 0;
    background: linear-gradient(rgba(255, 255, 255, 0.98), rgba(250, 250, 250, 0.98));
    padding: 20px;
    padding-top: 80px; /* Add top padding to avoid header overlap */
    display: fixed;
    flex-direction: column;
    justify-content: space-between;
    height: 100vh; /* Make sure it spans the full height */
    overflow-y: auto;
    position: relative;
    transition: left 0.3s ease;
    box-shadow: 2px 0 15px rgba(74, 71, 71, 0.4);
    border-right: 1px solid rgba(200, 196, 196, 0.6);
    box-sizing: border-box; /* Include padding in height calculation */
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .header {
    width: 100vw; /* Make the header span the full viewport width */
    left: 0; /* Align the header with the left edge of the screen */
    box-shadow: 0px 4px 5px rgba(74, 71, 71, 0.92);
  }

  .logo {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    margin-top: 0px; /* Move logo higher for mobile screens too */
  }


  /* Toggle Button Styles */
  .toggle-left-panel {
  display: block;
  position: absolute;
  border: none;
  border-radius: 5px;
  top: 10px;
  left: 10px;
  z-index: 150;
  color: black;
  font-size: 20px;
  width: 35px;
  height: 35px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: background 0.3s ease, color 0.3s ease;
  background: transparent;
  cursor: pointer;
}

.toggle-left-panel[disabled] {
  background: #ccc; /* Gray background for disabled state */
  color: black;
}

.toggle-left-panel:hover {
  background-color: lightgrey;
}

/* Left Panel Hidden by Default */
.left-panel {
  position: fixed;
  top: 0;
  left: -300px; /* Hidden off-screen */
  z-index: 20;
  height: 100%;
  width: 300px;
  background: linear-gradient(rgba(255, 255, 255, 0.98), rgba(250, 250, 250, 0.98));
  transition: left 0.3s ease;
  overflow-y: auto;
  box-shadow: 2px 0 15px rgba(74, 71, 71, 0.4);
  border-right: 1px solid rgba(200, 196, 196, 0.6);
  padding: 20px;
  padding-top: 80px; /* Add top padding to avoid header overlap on mobile */
  box-sizing: border-box; /* Include padding in height calculation */
}

/* Left Panel Visible */
.left-panel.show {
  left: 0; /* Slide into view */
}

/* Left Panel Hidden */
.left-panel.hide {
  left: -300px; /* Slide out of view */
}

.chatbot-container {
    margin-top: 60px; /* Adjusted for fixed header */
    flex-direction: column;
}

.chatbot-panel {
    padding: 10px;
}

.input-area {
  margin-bottom: 60px;
    gap: 10px;
}

.input {
    padding: 10px;
}
}

/* ========================================
   AUTHENTICATION PAGES RESPONSIVE STYLES
   ======================================== */

/* Auth Page Container */
.auth-page {
  min-height: 100vh;
  background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(200, 196, 196, 0.8));
  background-color: #f8f9fa; /* Fallback background */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

.auth-container {
  background: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95));
  padding: 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  border: 1px solid rgba(200, 196, 196, 0.3);
}

/* Responsive auth container */
@media (max-width: 480px) {
  .auth-container {
    padding: 1.5rem;
    margin: 1rem;
    max-width: none;
    border-radius: 8px;
  }

  .auth-page {
    padding: 0.5rem;
  }
}

@media (max-width: 320px) {
  .auth-container {
    padding: 1rem;
    margin: 0.5rem;
  }
}

/* Logo Section */
.logo-section {
  text-align: center;
  margin-bottom: 2rem;
}

.logo-section h1 {
  margin: 0.5rem 0;
  font-size: 1.75rem;
  font-weight: bold;
  /* Gradient Effect - same as chatbot header */
  background: linear-gradient(to right, #f10606, #66667a);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.logo-section p {
  color: #666;
  margin: 0;
  font-size: 0.9rem;
}

@media (max-width: 480px) {
  .logo-section h1 {
    font-size: 1.5rem;
  }

  .logo-section p {
    font-size: 0.8rem;
  }
}

/* Auth Tabs */
.auth-tabs {
  display: flex;
  margin-bottom: 2rem;
  border-bottom: 1px solid #eee;
}

.tab {
  flex: 1;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.3s ease;
  font-size: 1rem;
}

.tab.active {
  border-bottom-color: #007bff;
  color: #007bff;
  font-weight: bold;
}

@media (max-width: 480px) {
  .tab {
    padding: 0.75rem 0.5rem;
    font-size: 0.9rem;
  }
}

/* Form Elements */
.form-group {
  margin-bottom: 1.25rem;
}

.form-group label {
  display: block;
  margin-bottom: 0.5rem;
  color: #333;
  font-weight: 500;
  font-size: 0.9rem;
}

.form-group input {
  width: 100%;
  padding: 0.75rem;
  border: 2px solid grey;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.3s ease;
  font-family: inherit;
}

.form-group input:focus {
  outline: none;
  border-color: #007bff;
}

@media (max-width: 480px) {
  .form-group input {
    padding: 0.6rem;
    font-size: 0.9rem;
  }

  .form-group label {
    font-size: 0.8rem;
  }
}

/* Submit Button */
.submit-btn {
  width: 100%;
  padding: 0.75rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  font-family: inherit;
}

.submit-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

@media (max-width: 480px) {
  .submit-btn {
    padding: 0.6rem;
    font-size: 0.9rem;
  }
}

/* Messages */
.error, .message {
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1.25rem;
  text-align: center;
  font-size: 0.9rem;
}

.error {
  background: #fee;
  color: #c33;
  border: 1px solid #fcc;
}

.message {
  background: #efe;
  color: #363;
  border: 1px solid #cfc;
}

@media (max-width: 480px) {
  .error, .message {
    padding: 0.6rem;
    font-size: 0.8rem;
  }
}

/* Navigation Links */
.back-link, .admin-link {
  text-align: center;
  margin-top: 1rem;
}

.back-link a, .admin-link a {
  color: #007bff;
  text-decoration: none;
  font-size: 0.9rem;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 20px;
  transition: all 0.3s ease;
}

.back-link a:hover, .admin-link a:hover {
  background-color: rgba(0, 123, 255, 0.1);
  text-decoration: none;
}

.admin-link {
  padding-top: 1rem;
  border-top: 1px solid #eee;
}

.admin-link a {
  color: #6c757d;
  font-size: 0.8rem;
}

.admin-link a:hover {
  background-color: #f8f9fa;
  color: #495057;
}

@media (max-width: 480px) {
  .back-link a, .admin-link a {
    font-size: 0.8rem;
    padding: 0.4rem 0.8rem;
  }

  .admin-link a {
    font-size: 0.75rem;
  }
}

/* Domain Info */
.domain-info {
  background: #f8f9fa;
  padding: 1rem;
  border-radius: 5px;
  margin-bottom: 1.25rem;
  text-align: center;
  font-size: 0.85rem;
  color: #666;
  border: 1px solid #e9ecef;
}

@media (max-width: 480px) {
  .domain-info {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
}

/* ========================================
   EMAIL VERIFICATION PAGE RESPONSIVE STYLES
   ======================================== */

/* Verify Page Container */
.verify-page {
  min-height: 100vh;
  background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(200, 196, 196, 0.8));
  background-color: #f8f9fa; /* Fallback background */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 1rem;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

.verify-container {
  background: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95));
  padding: 2.5rem;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
  width: 100%;
  max-width: 500px;
  text-align: center;
  margin: 0 auto;
  border: 1px solid rgba(200, 196, 196, 0.3);
}

@media (max-width: 480px) {
  .verify-container {
    padding: 1.5rem;
    margin: 1rem;
    max-width: none;
    border-radius: 8px;
  }

  .verify-page {
    padding: 0.5rem;
  }
}

@media (max-width: 320px) {
  .verify-container {
    padding: 1rem;
    margin: 0.5rem;
  }
}

/* Verification Icons */
.icon {
  font-size: 4rem;
  margin-bottom: 1.25rem;
}

.icon.verifying {
  color: #007bff;
  animation: spin 2s linear infinite;
}

.icon.success {
  color: #28a745;
}

.icon.error {
  color: #dc3545;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@media (max-width: 480px) {
  .icon {
    font-size: 3rem;
    margin-bottom: 1rem;
  }
}

/* Verification Content */
.verify-container h1 {
  color: #333;
  margin-bottom: 1.25rem;
  font-size: 1.75rem;
}

.verify-container .message {
  color: #666;
  margin-bottom: 2rem;
  line-height: 1.6;
  font-size: 1rem;
}

@media (max-width: 480px) {
  .verify-container h1 {
    font-size: 1.5rem;
    margin-bottom: 1rem;
  }

  .verify-container .message {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
  }
}

/* Resend Section */
.resend-section {
  background: #f8f9fa;
  padding: 1.25rem;
  border-radius: 5px;
  margin-bottom: 1.25rem;
  border: 1px solid #e9ecef;
}

.resend-section h3 {
  color: #333;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

@media (max-width: 480px) {
  .resend-section {
    padding: 1rem;
  }

  .resend-section h3 {
    font-size: 1rem;
    margin-bottom: 0.75rem;
  }
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  text-decoration: none;
  display: inline-block;
  margin: 0.25rem;
  transition: transform 0.2s ease;
  font-family: inherit;
}

.btn:hover:not(:disabled) {
  transform: translateY(-2px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background-color: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

@media (max-width: 480px) {
  .btn {
    padding: 0.6rem 1.2rem;
    font-size: 0.9rem;
    margin: 0.2rem;
  }
}

/* Actions Section */
.actions {
  margin-top: 2rem;
}

@media (max-width: 480px) {
  .actions {
    margin-top: 1.5rem;
  }

  .actions .btn {
    width: 100%;
    margin: 0.25rem 0;
  }
}

/* ========================================
   PROTECTED ROUTE LOADING STYLES
   ======================================== */

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(200, 196, 196, 0.8));
  background-color: #f8f9fa; /* Fallback background */
  padding: 1rem;
}

.loading-content {
  text-align: center;
  color: #333;
  background: linear-gradient(rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.9));
  padding: 2rem;
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
}

.spinner {
  font-size: 3rem;
  animation: spin 2s linear infinite;
  margin-bottom: 1.25rem;
  color: #007bff;
}

.loading-text {
  font-size: 1.125rem;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

@media (max-width: 480px) {
  .spinner {
    font-size: 2.5rem;
    margin-bottom: 1rem;
  }

  .loading-text {
    font-size: 1rem;
  }
}

/* ========================================
   RESPONSIVE UTILITIES
   ======================================== */

/* Hide elements on mobile */
@media (max-width: 768px) {
  .hide-mobile {
    display: none !important;
  }
}

/* Hide elements on desktop */
@media (min-width: 769px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Text utilities */
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

/* Responsive text sizes */
.text-sm {
  font-size: 0.875rem;
}

.text-base {
  font-size: 1rem;
}

.text-lg {
  font-size: 1.125rem;
}

.text-xl {
  font-size: 1.25rem;
}

@media (max-width: 480px) {
  .text-sm {
    font-size: 0.8rem;
  }

  .text-base {
    font-size: 0.9rem;
  }

  .text-lg {
    font-size: 1rem;
  }

  .text-xl {
    font-size: 1.1rem;
  }
}

/* Spacing utilities */
.mt-1 { margin-top: 0.25rem; }
.mt-2 { margin-top: 0.5rem; }
.mt-3 { margin-top: 0.75rem; }
.mt-4 { margin-top: 1rem; }

.mb-1 { margin-bottom: 0.25rem; }
.mb-2 { margin-bottom: 0.5rem; }
.mb-3 { margin-bottom: 0.75rem; }
.mb-4 { margin-bottom: 1rem; }

.p-1 { padding: 0.25rem; }
.p-2 { padding: 0.5rem; }
.p-3 { padding: 0.75rem; }
.p-4 { padding: 1rem; }

/* Responsive containers */
.container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 1rem;
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.5rem;
  }
}

/* ========================================
   ADMIN PAGES STYLES
   ======================================== */

/* Admin Page Container */
.admin-page {
  min-height: 100vh;
  background: linear-gradient(rgba(255, 255, 255, 0.8), rgba(200, 196, 196, 0.8));
  background-color: #f8f9fa;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
}

/* Admin Header */
.admin-header {
  background: linear-gradient(to right, #f10606, #66667a);
  color: white;
  padding: 1rem 0;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.header-content h1 {
  margin: 0;
  font-size: 1.75rem;
  font-weight: bold;
  flex: 1;
  text-align: center;
}

.logout-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.logout-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: rgba(255, 255, 255, 0.9);
}

/* Admin Container */
.admin-container {
  max-width: 800px;
  margin: 2rem auto;
  padding: 2rem;
  background: linear-gradient(rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.95));
  border-radius: 8px;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.92);
  border: 1px solid rgba(200, 196, 196, 0.3);
}

.dashboard-section {
  text-align: center;
  margin-bottom: 2rem;
}

.dashboard-section h2 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.5rem;
}

.dashboard-section p {
  color: #666;
  margin: 0;
}

/* Upload Section */
.upload-section {
  margin-top: 2rem;
}

.dropzone {
  border: 2px dashed #007bff;
  border-radius: 8px;
  padding: 2rem;
  text-align: center;
  cursor: pointer;
  background-color: rgba(0, 123, 255, 0.05);
  transition: all 0.3s ease;
  margin-bottom: 1.5rem;
}

.dropzone:hover {
  background-color: rgba(0, 123, 255, 0.1);
  border-color: #0056b3;
}

.dropzone-content i {
  font-size: 3rem;
  color: #007bff;
  margin-bottom: 1rem;
  display: block;
}

.dropzone-content p {
  font-size: 1.1rem;
  color: #333;
  margin: 0 0 0.5rem 0;
}

.dropzone-content small {
  color: #666;
  font-size: 0.9rem;
}

/* Selected Files */
.selected-files {
  margin-bottom: 1.5rem;
}

.selected-files h3 {
  color: #007bff;
  margin-bottom: 1rem;
  font-size: 1.1rem;
}

.file-list {
  background: #f8f9fa;
  border-radius: 5px;
  padding: 1rem;
  border: 1px solid #e9ecef;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  margin-bottom: 0.5rem;
  background: white;
  border-radius: 5px;
  border: 1px solid #dee2e6;
}

.file-item:last-child {
  margin-bottom: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.file-info i {
  color: #007bff;
  font-size: 1.1rem;
}

.file-info span {
  color: #333;
  font-weight: 500;
}

.remove-file-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 3px;
  transition: background-color 0.3s ease;
}

.remove-file-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

/* Upload Button */
.upload-btn {
  width: 100%;
  padding: 0.75rem 1.5rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  font-size: 1rem;
  font-weight: bold;
  cursor: pointer;
  transition: background-color 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-family: inherit;
}

.upload-btn:hover:not(:disabled) {
  background-color: #0056b3;
}

.upload-btn:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

/* Error Messages */
.admin-container .error {
  background: #fee;
  color: #c33;
  border: 1px solid #fcc;
  padding: 0.75rem;
  border-radius: 5px;
  margin-bottom: 1rem;
  text-align: center;
}

/* Responsive Design */
@media (max-width: 768px) {
  .header-content {
    padding: 0 1rem;
  }

  .header-content h1 {
    font-size: 1.5rem;
  }

  .admin-container {
    margin: 1rem;
    padding: 1.5rem;
  }

  .dropzone {
    padding: 1.5rem;
  }

  .dropzone-content i {
    font-size: 2.5rem;
  }

  .file-item {
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .header-content {
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }

  .header-content h1 {
    font-size: 1.25rem;
  }

  .admin-container {
    margin: 0.5rem;
    padding: 1rem;
  }

  .dropzone {
    padding: 1rem;
  }

  .dropzone-content i {
    font-size: 2rem;
  }

  .file-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .remove-file-btn {
    align-self: flex-end;
  }
}

/* ========================================
   RECENT CHATS SECTION
   ======================================== */

/* Left Panel Layout Fix */
.left-panel {
  display: flex;
  flex-direction: column;
  height: 100vh;
  overflow: hidden;
}

.left-panel-bottom {
  margin-top: auto;
  border-top: 1px solid #eee;
  padding-top: 1rem;
  flex-shrink: 0; /* Prevent shrinking */
}

/* Social Media Links - Compact Design */
.social-media-links {
  text-align: center;
}

.social-media-links h3 {
  color: #007bff;
  font-size: 0.8rem;
  margin-bottom: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.social-icons {
  display: flex;
  justify-content: center;
  gap: 0.75rem;
  flex-wrap: wrap;
}

.social-icons a {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  color: white;
  border-radius: 50%;
  text-decoration: none;
  transition: all 0.3s ease;
  font-size: 0.9rem;
}

.social-icons a:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
}

/* Recent Chats Section */
.recent-chats-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  margin: 1rem 0;
  border-top: 1px solid #eee;
  padding-top: 1rem;
  min-height: 0; /* Important for flex child to shrink */
}

.recent-chats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 5px 10px;
  margin-bottom: 0.5rem;
}

.recent-chats-section h3 {
  color: #007bff;
  font-size: 0.9rem;
  margin: 0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  flex-shrink: 0;
}

.sync-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.manual-sync-btn {
  background: none;
  border: none;
  color: #007bff;
  cursor: pointer;
  padding: 4px;
  border-radius: 3px;
  transition: all 0.3s ease;
  font-size: 0.8rem;
}

.manual-sync-btn:hover {
  background: rgba(0, 123, 255, 0.1);
  transform: rotate(180deg);
}

.sync-indicator {
  color: #007bff;
  font-size: 0.8rem;
  animation: pulse 1.5s ease-in-out infinite;
}

.sync-indicator i {
  animation: spin 1s linear infinite;
}

.sync-success {
  color: #28a745;
  font-size: 0.8rem;
  animation: fadeIn 0.5s ease-in;
}

.sync-error {
  color: #dc3545;
  font-size: 0.8rem;
  animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.recent-chats-list {
  flex: 1;
  overflow-y: auto;
  min-height: 0;
}

.no-chats {
  padding: 1rem;
  text-align: center;
  color: #666;
  font-style: italic;
}

.no-chats p {
  margin: 0;
  font-size: 0.85rem;
}

.recent-chat-item {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  margin-bottom: 0.25rem;
  border-radius: 5px;
  cursor: pointer;
  transition: background-color 0.2s ease;
  border: 1px solid transparent;
}

.recent-chat-item:hover {
  background-color: rgba(0, 123, 255, 0.1);
}

.recent-chat-item.active {
  background-color: rgba(0, 123, 255, 0.15);
  border-color: #007bff;
}

.chat-item-content {
  flex: 1;
  min-width: 0;
}

.chat-title {
  font-size: 0.8rem;
  color: #333;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px; /* Fixed width to prevent panel disturbance */
  line-height: 1.2;
}

.chat-timestamp {
  font-size: 0.7rem;
  color: #666;
  margin-top: 0.2rem;
}

.delete-chat-btn {
  background: none;
  border: none;
  color: #dc3545;
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 3px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.recent-chat-item:hover .delete-chat-btn {
  opacity: 1;
}

.delete-chat-btn:hover {
  background-color: rgba(220, 53, 69, 0.1);
}

/* Enhanced New Chat Button */
.new-chat-btn {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  width: 100%;
  padding: 0.75rem;
  background-color: #007bff;
  color: white;
  border: none;
  border-radius: 5px;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.3s ease;
  margin-bottom: 1rem;
  font-family: 'Lucida Sans', 'Lucida Sans Regular', 'Lucida Grande', 'Lucida Sans Unicode', Geneva, Verdana, sans-serif;
  font-size: large;
  margin-top: -30px; /* Moved down a bit */
}

.new-chat-btn:hover {
  background-color: #0056b3;
}

.new-chat-btn i {
  font-size: 0.9rem;
}

/* ========================================
   WELCOME CONTAINER & SUGGESTION CARDS
   ======================================== */

/* Welcome Container */
.welcome-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 2rem;
  text-align: center;
}

.welcome-header {
  margin-bottom: 2rem;
}

.welcome-icon {
  font-size: 4rem;
  color: #007bff;
  margin-bottom: 1rem;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.welcome-header h2 {
  color: #333;
  margin-bottom: 0.5rem;
  font-size: 1.75rem;
  font-weight: bold;
}

.welcome-header p {
  color: #666;
  font-size: 1rem;
  line-height: 1.6;
  max-width: 500px;
}

/* Suggestion Cards - Further Reduced Size */
.suggestion-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 0.6rem;
  max-width: 700px;
  width: 100%;
}

.suggestion-card {
  background: white;
  border: 2px solid #f0f0f0;
  border-radius: 6px;
  padding: 0.75rem;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: left;
  box-shadow: 0 2px 5px rgba(74, 71, 71, 0.1);
}

.suggestion-card:hover {
  border-color: #007bff;
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 123, 255, 0.15);
}

.suggestion-icon {
  width: 35px;
  height: 35px;
  background: linear-gradient(135deg, #007bff, #0056b3);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.6rem;
}

.suggestion-icon i {
  color: white;
  font-size: 0.9rem;
}

.suggestion-card h4 {
  color: #333;
  margin: 0 0 0.3rem 0;
  font-size: 0.9rem;
  font-weight: bold;
}

.suggestion-card p {
  color: #666;
  margin: 0;
  font-size: 0.75rem;
  line-height: 1.2;
}

/* Responsive adjustments for recent chats and left panel */
@media (max-width: 768px) {
  .recent-chat-item {
    padding: 0.4rem;
  }

  .chat-title {
    font-size: 0.75rem;
    max-width: 120px; /* Smaller on mobile */
  }

  .chat-timestamp {
    font-size: 0.65rem;
  }

  .social-icons {
    gap: 0.5rem;
  }

  .social-icons a {
    width: 28px;
    height: 28px;
    font-size: 0.8rem;
  }

  .left-panel-bottom {
    padding-top: 0.75rem;
    margin-top: 1rem; /* Move Follow Us section up on mobile */
  }

  /* Welcome container mobile adjustments */
  .welcome-container {
    padding: 1rem;
  }

  .welcome-header h2 {
    font-size: 1.5rem;
  }

  .welcome-icon {
    font-size: 3rem;
  }

  .suggestion-cards {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .suggestion-card {
    padding: 0.6rem;
  }

  .suggestion-icon {
    width: 30px;
    height: 30px;
  }

  .suggestion-icon i {
    font-size: 0.8rem;
  }
}


